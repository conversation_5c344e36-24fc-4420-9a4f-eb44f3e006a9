import React, {useState, useEffect, useRef, useMemo, useCallback} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import MapView, {Callout, Marker, PROVIDER_GOOGLE, Region} from 'react-native-maps';
import {MapPin} from '@/data/mapPins';
import {darkMapStyle} from '@/utils/mapStyles';
import {getCurrentPosition} from '@/utils/locationService';
import {Icon} from '@/components';
import CustomMarker from './CustomMarker';
import {useThemeStore} from '@/store';
import PlayerSchedulePlayCard from '../PlayerSchedulePlayCard';

const {height} = Dimensions.get('window');

const isAndroid = Platform.OS === 'android';

// Maximum number of markers to render at once to prevent memory issues
const MAX_VISIBLE_MARKERS = 50;

interface CustomMapViewProps {
  pins: MapPin[];
  initialRegion?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  showUserLocation?: boolean;
  style?: React.CSSProperties | React.CSSProperties[] | object;
  markerType?: 'player' | 'coach' | 'shop' | 'default';
  onMarkerPress?: (pin: MapPin) => void;
}

const CustomMapView: React.FC<CustomMapViewProps> = ({
  pins,
  initialRegion = {
    latitude: 40.7128,
    longitude: -74.006,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  showUserLocation = true,
  style,
  markerType = 'default',
  onMarkerPress,
}) => {
  const theme = useThemeStore();
  const mapRef = useRef<MapView>(null);
  const [userLocation, setUserLocation] = useState<{latitude: number; longitude: number} | null>(
    null,
  );
  // Using underscore prefix to indicate unused state variable
  const [_isAtUserLocation, setIsAtUserLocation] = useState(true);
  const [showLocationButton, setShowLocationButton] = useState(false);
  // Initialize visibleRegion with initialRegion to show pins immediately
  const [visibleRegion, setVisibleRegion] = useState<Region | null>(initialRegion);
  const [loadingLocation, setLoadingLocation] = useState(true);
  const [pinsLoaded, setPinsLoaded] = useState(false);

  // Track if component is mounted to prevent memory leaks
  const isMounted = useRef(true);

  // Get user's current location
  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        if (isAndroid) {
          await new Promise(resolve => setTimeout(resolve, 500)); // Android delay
        }

        const position = await getCurrentPosition();
        if (position && isMounted.current) {
          setUserLocation(position);
          setIsAtUserLocation(true);
          setShowLocationButton(false);
        }
      } catch (error) {
        console.error('Error getting user location:', error);
        if (isMounted.current) {
          setShowLocationButton(true);
        }
      } finally {
        if (isMounted.current) {
          setLoadingLocation(false);
        }
      }
    };

    if (showUserLocation) {
      fetchUserLocation();
    }

    // Cleanup function
    return () => {
      isMounted.current = false;
    };
  }, [showUserLocation]);

  // Cleanup function when component unmounts
  useEffect(() => {
    return () => {
      isMounted.current = false;
      setVisibleRegion(null);
      setUserLocation(null);
    };
  }, []);

  // Effect to handle pins loading
  useEffect(() => {
    if (pins && pins.length > 0 && isMounted.current) {
      // Small delay to ensure pins are processed properly
      const timer = setTimeout(() => {
        if (isMounted.current) {
          setPinsLoaded(true);
        }
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [pins]);

  const isMapAtUserLocation = useCallback(
    (region: Region): boolean => {
      if (!userLocation) return false;
      const latDiff = Math.abs(region.latitude - userLocation.latitude);
      const lngDiff = Math.abs(region.longitude - userLocation.longitude);
      return latDiff < 0.001 && lngDiff < 0.001;
    },
    [userLocation],
  );

  const handleRegionChange = useCallback(
    (region: Region) => {
      if (!isMounted.current) return;

      setVisibleRegion(region);

      if (userLocation) {
        const atUserLocation = isMapAtUserLocation(region);
        setIsAtUserLocation(atUserLocation);
        setShowLocationButton(!atUserLocation);
      }
    },
    [userLocation, isMapAtUserLocation],
  );

  const centerOnUserLocation = useCallback(async () => {
    if (userLocation && mapRef.current) {
      mapRef.current.animateToRegion(
        {
          latitude: userLocation.latitude,
          longitude: userLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        },
        1000,
      );
      setIsAtUserLocation(true);
      setShowLocationButton(false);
    } else {
      try {
        const position = await getCurrentPosition();
        if (position && mapRef.current && isMounted.current) {
          setUserLocation(position);
          mapRef.current.animateToRegion(
            {
              latitude: position.latitude,
              longitude: position.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            },
            1000,
          );
          setIsAtUserLocation(true);
          setShowLocationButton(false);
        }
      } catch (error) {
        console.error('Error getting user location:', error);
      }
    }
  }, [userLocation]);

  // Optimizing visible pins based on the map's current region
  const isMarkerVisible = useCallback(
    (coordinate: {latitude: number; longitude: number}): boolean => {
      // Use visibleRegion if available, otherwise fall back to initialRegion
      const region = visibleRegion || initialRegion;

      const {latitude, longitude} = coordinate;
      const {latitude: centerLat, longitude: centerLng, latitudeDelta, longitudeDelta} = region;

      const latMin = centerLat - latitudeDelta / 2;
      const latMax = centerLat + latitudeDelta / 2;
      const lngMin = centerLng - longitudeDelta / 2;
      const lngMax = centerLng + longitudeDelta / 2;

      // Reduced padding to show fewer markers
      const latPadding = latitudeDelta * 0.1;
      const lngPadding = longitudeDelta * 0.1;

      return (
        latitude >= latMin - latPadding &&
        latitude <= latMax + latPadding &&
        longitude >= lngMin - lngPadding &&
        longitude <= lngMax + lngPadding
      );
    },
    [visibleRegion, initialRegion],
  );

  // Memoize the visible pins so they are only recalculated when necessary
  // Limit the number of visible pins to prevent memory issues
  const visiblePins = useMemo(() => {
    if (!pins) return [];

    // Use visibleRegion if available, otherwise fall back to initialRegion
    const region = visibleRegion || initialRegion;

    // If there are too many pins, use a more aggressive filtering strategy
    if (pins.length > MAX_VISIBLE_MARKERS * 2) {
      // First filter by visibility, then take only the closest ones to the center
      const centerLat = region.latitude;
      const centerLng = region.longitude;

      return (
        pins
          .filter(pin => isMarkerVisible(pin.coordinate))
          // Sort by distance to center (approximate calculation for better performance)
          .sort((a, b) => {
            const distA =
              Math.pow(a.coordinate.latitude - centerLat, 2) +
              Math.pow(a.coordinate.longitude - centerLng, 2);
            const distB =
              Math.pow(b.coordinate.latitude - centerLat, 2) +
              Math.pow(b.coordinate.longitude - centerLng, 2);
            return distA - distB;
          })
          .slice(0, MAX_VISIBLE_MARKERS)
      );
    }

    // For fewer pins, just filter by visibility
    const filtered = pins
      .filter(pin => isMarkerVisible(pin.coordinate))
      .slice(0, MAX_VISIBLE_MARKERS);

    return filtered;
  }, [pins, visibleRegion, initialRegion, isMarkerVisible]);

  return (
    <View style={[styles(theme).container, style]}>
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles(theme).mapStyle}
        initialRegion={initialRegion}
        customMapStyle={darkMapStyle}
        rotateEnabled={false}
        zoomControlEnabled={false}
        scrollEnabled={pinsLoaded} // Only enable scrolling after pins are loaded
        zoomEnabled={pinsLoaded} // Only enable zooming after pins are loaded
        toolbarEnabled={isAndroid ? false : undefined}
        moveOnMarkerPress={false}
        showsCompass={false}
        showsScale={false}
        pitchEnabled={false}
        followsUserLocation={false}
        showsUserLocation={showUserLocation && !loadingLocation} // Show location once fetched
        onRegionChangeComplete={handleRegionChange}
        onMapReady={() => {
          // Ensure map is ready for interaction
          if (isMounted.current && !pinsLoaded && pins.length > 0) {
            setPinsLoaded(true);
          }
        }}>
        {visiblePins.map(pin => {
          return (
            <Marker
              key={pin.id}
              coordinate={pin.coordinate}
              tracksViewChanges={false}
              onPress={e => {
                // Prevent default behavior that might show a callout
                e.stopPropagation();
                onMarkerPress?.(pin);
              }}
              // Important for performance
            >
              <CustomMarker
                type={
                  markerType === 'player'
                    ? 'player'
                    : (pin.type as 'available_kiosk' | 'planned_location')
                }
                pin={pin}
              />
              {/* {markerType === 'player' && (
                <Callout tooltip={true}>
                  <View style={{width: Dimensions.get('window').width * 0.9}}>
                    <PlayerSchedulePlayCard
                      playerName={pin.title}
                      location={pin.description}
                      image={pin.image}
                      onPress={() => {
                        console.log('player pressed');
                      }}
                    />
                  </View>
                </Callout>
              )} */}
            </Marker>
          );
        })}
      </MapView>

      {/* Loading indicator while pins are loading */}
      {!pinsLoaded && pins.length > 0 && (
        <View style={styles(theme).loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.activeColor} />
        </View>
      )}

      {/* Show location button if needed */}
      {showUserLocation && showLocationButton && !loadingLocation && (
        <TouchableOpacity
          style={styles(theme).centerButton}
          onPress={centerOnUserLocation}
          activeOpacity={0.8}>
          <Icon name="location" size={20} color="#666666" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = (theme: any) =>
  StyleSheet.create({
    loadingContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      zIndex: 10,
    },
    container: {
      flex: 1,
      position: 'relative',
      height: '100%',
      width: '100%',
    },
    mapStyle: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    centerButton: {
      position: 'absolute',
      top: height / 15,
      right: 12,
      backgroundColor: 'white',
      borderRadius: 8,
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      // zIndex: 999,
    },
  });

export default CustomMapView;
