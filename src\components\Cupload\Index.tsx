import React, {useState, useEffect, useCallback} from 'react';
import {View, Alert, StyleSheet, Platform, Linking, TouchableOpacity} from 'react-native';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {check, request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import CImage from '../CImage';
import CIcon from '../CIcon';
import CustomModal from '../Modal';
import Typography from '../Typography';
import {useThemeStore} from '@/store';
import {isArray, isEmpty} from 'lodash';
import {IOS} from '@/utils/commonFunctions';
import CButton from '../CButton';
import {Icons} from '@/config/icons';

interface UploadComponentProps {
  allowGallerySelection?: boolean;
  allowCameraCapture?: boolean;
  allowUpload?: boolean;
  onImageSelected?: (uri: string | null) => void;
  value?: string | null;
  containerStyle?: object;
  imageContentStyle?: object;
}

interface ImageOption {
  id: number;
  optionTitle: string;
  handleClick: () => void;
  icon: string;
}

interface RequestPermissionsOptions {
  camera: boolean;
  album: boolean;
}

const UploadComponent: React.FC<UploadComponentProps> = ({
  allowGallerySelection = false,
  allowCameraCapture = false,
  allowUpload = false,
  onImageSelected,
  imageContentStyle,
  containerStyle,
  value = null,
}) => {
  const theme = useThemeStore();
  const [imageUri, setImageUri] = useState<string | null>(value);
  const [hasCameraPermission, setHasCameraPermission] = useState(false);
  const [hasGalleryPermission, setHasGalleryPermission] = useState(false);
  const [visible, setVisible] = useState(false);

  const imageOptionsArray: ImageOption[] = [
    {
      id: 1,
      optionTitle: 'Open camera',
      handleClick: () => capturePhoto(),
      icon: 'camera-1',
    },
    {
      id: 2,
      optionTitle: 'Open gallery',
      handleClick: () => pickImageFromGallery(),
      icon: 'camera-1',
    },
  ];

  // Log visible state changes
  useEffect(() => {
    console.log('Visible state changed:', visible);
  }, [visible]);

  // Notify parent of image URI changes
  useEffect(() => {
    if (onImageSelected) {
      onImageSelected(imageUri);
    }
  }, [imageUri, onImageSelected]);

  // Function to show alert prompting user to open settings
  const openSettingsAlert = useCallback(({title}) => {
    Alert.alert(title, 'Please enable the required permission in your device settings.', [
      {
        text: 'Open Settings',
        onPress: () => Linking.openSettings(),
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ]);
  }, []);

  // Request permissions on component mount, based on props

  const requestPermissions = async (
    camera: boolean,
    album: boolean,
  ): Promise<{camera: boolean; album: boolean}> => {
    const cameraPermission = IOS ? PERMISSIONS.IOS.CAMERA : PERMISSIONS.ANDROID.CAMERA;
    const galleryPermission = IOS
      ? PERMISSIONS.IOS.PHOTO_LIBRARY
      : Platform.Version >= 33
        ? PERMISSIONS.ANDROID.READ_MEDIA_IMAGES
        : PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;

    let cameraGranted = true;
    let galleryGranted = true;

    if (camera) {
      let cameraStatus = await check(cameraPermission);
      if (cameraStatus !== RESULTS.GRANTED) {
        cameraStatus = await request(cameraPermission);
      }

      if (cameraStatus !== RESULTS.GRANTED) {
        cameraGranted = false;
        openSettingsAlert({title: 'Camera Permission Denied'});
      }
    }

    if (album) {
      let galleryStatus = await check(galleryPermission);
      if (galleryStatus !== RESULTS.GRANTED) {
        galleryStatus = await request(galleryPermission);
      }

      if (galleryStatus !== RESULTS.GRANTED) {
        galleryGranted = false;
        openSettingsAlert({title: 'Gallery Permission Denied'});
      }
    }

    return {camera: cameraGranted, album: galleryGranted};
  };

  // Function to pick an image from the gallery
  const pickImageFromGallery = async () => {
    const {album} = await requestPermissions(false, true);
    if (album) {
      launchImageLibrary(
        {
          mediaType: 'photo',
          quality: 1,
          includeBase64: false,
        },
        response => {
          if (response.didCancel) {
            console.log('User cancelled image picker');
          } else if (response.errorCode) {
            Alert.alert('Error', `Image picker error: ${response.errorMessage}`);
          } else if (response.assets && response.assets.length > 0) {
            setImageUri(response.assets[0].uri);
            setVisible(false);
          }
        },
      );
    }
  };

  // Function to capture a photo from the camera
  const capturePhoto = async () => {
    const {camera} = await requestPermissions(true, false);
    if (camera) {
      launchCamera(
        {
          mediaType: 'photo',
          quality: 1,
          includeBase64: false,
        },
        response => {
          if (response.didCancel) {
            console.log('User cancelled camera');
          } else if (response.errorCode) {
            Alert.alert('Error', `Camera error: ${response.errorMessage}`);
          } else if (response.assets && response.assets.length > 0) {
            setImageUri(response.assets[0].uri);
            setVisible(false);
          }
        },
      );
    }
  };

  // Placeholder function to upload the image
  const uploadImage = async () => {
    if (!imageUri) {
      Alert.alert('No Image', 'Please select or capture an image first.');
      return;
    }

    Alert.alert('Upload', 'Image upload functionality placeholder.');
  };

  return (
    <View style={[containerStyle, styles.modalContainer]}>
      <TouchableOpacity onPress={() => setVisible(true)}>
        {imageUri ? (
          <View style={[styles.imageContainer, imageContentStyle]}>
            <CImage source={{uri: imageUri}} style={styles.image} pointerEvents="none" />
          </View>
        ) : (
          <CIcon name="camera-1" size={24} color={theme.colors.black} />
        )}
      </TouchableOpacity>

      <CustomModal
        variant="bottom"
        visible={visible}
        onClose={() => setVisible(false)}
        title={'Choose a photo from your gallery or take a new one with your camera.'}
        titleStyle={{color: theme.colors.text}}
        modalContainerStyle={{backgroundColor: theme.colors.background}}>
        <View style={styles.importContainer}>
          {!isEmpty(imageOptionsArray) &&
            isArray(imageOptionsArray) &&
            imageOptionsArray.map((item, index) => (
              <TouchableOpacity
                key={`options_${item.id}_${index}`}
                onPress={item.handleClick}
                style={styles.importBlock}>
                {index === 0 ? (
                  <CIcon name={item.icon} size={32} color={theme.colors.white} />
                ) : (
                  <Icons.Entypo name="images" size={28} color={theme.colors.white} />
                )}
                <Typography color={theme.colors.white}>{item.optionTitle}</Typography>
              </TouchableOpacity>
            ))}
        </View>
      </CustomModal>
    </View>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    overflow: 'hidden',
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  importContainer: {
    flexDirection: 'row',
    gap: 20,
    justifyContent: 'center',
    marginVertical: 10,
  },
  importBlock: {
    backgroundColor: '#333',
    borderRadius: 12,
    gap: 10,
    flex: 0.5,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 30,
  },
});

export default UploadComponent;
